<!-- 消息输入组件 -->
<template>
  <div class="bg-white border-t border-gray-200 min-h-48 p-2" v-if="currentContact">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center gap-2">
        <button 
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100" 
          @click="$emit('insert-emoji')"
        >
          😊
        </button>
        <button 
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100" 
          @click="$emit('attach-file')"
        >
          📎
        </button>
        <button 
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100" 
          @click="$emit('insert-image')"
        >
          🖼️
        </button>
      </div>
      <button
        class="bg-blue-500 text-white border-none px-4 py-2 rounded-md cursor-pointer text-sm font-medium hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        @click="handleSend"
        :disabled="!messageContent.trim()"
      >
        发送 (Ctrl+Enter)
      </button>
    </div>
    <div class="border border-gray-300 rounded-md bg-white">
      <textarea
        ref="messageEditor"
        v-model="messageContent"
        class="w-full border-none outline-none p-3 text-sm font-inherit resize-y min-h-28 placeholder-gray-400"
        placeholder="输入消息..."
        @keydown="handleKeyDown"
        rows="6"
      ></textarea>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  currentContact: Contact | null
}

defineProps<Props>()

defineEmits<{
  'send-message': [content: string]
  'insert-emoji': []
  'attach-file': []
  'insert-image': []
}>()

const messageContent = ref('')
const messageEditor = ref<HTMLTextAreaElement>()

const handleSend = () => {
  if (!messageContent.value.trim()) {
    return
  }
  
  // 发送消息
  const content = messageContent.value.trim()
  messageContent.value = ''
  
  // 触发发送事件
  emit('send-message', content)
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
}

// 获取emit函数
const emit = defineEmits<{
  'send-message': [content: string]
  'insert-emoji': []
  'attach-file': []
  'insert-image': []
}>()

// 暴露方法给父组件
defineExpose({
  focus: () => messageEditor.value?.focus(),
  clear: () => { messageContent.value = '' }
})
</script>
