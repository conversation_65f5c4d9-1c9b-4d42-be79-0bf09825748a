<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
    <!-- 左侧聊天列表 -->
    <div class="w-80 h-screen bg-white border-r border-gray-200 flex flex-col">
      <div class="p-4 border-b border-gray-200">
        <div class="relative">
          <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-base"
            >🔍</span
          >
          <input
            type="text"
            class="w-full pl-9 pr-10 py-2 bg-gray-100 border-none rounded-md outline-none text-sm"
            placeholder="搜索"
            v-model="searchQuery"
            @input="handleSearch"
          />
          <button
            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-transparent border-none cursor-pointer p-1 rounded text-gray-500 hover:bg-gray-100"
            @click="handleAddChat"
            title="刷新联系人列表"
          >
            🔄
          </button>
        </div>
      </div>

      <div class="flex-1 overflow-y-auto">
        <!-- 加载状态 -->
        <div v-if="isLoadingUsers" class="flex items-center justify-center p-8">
          <div class="text-gray-500 text-sm">加载联系人中...</div>
        </div>

        <!-- 聊天列表 -->
        <div v-else>
          <div
            v-for="chat in filteredChats"
            :key="chat.id"
            class="flex items-center p-3 cursor-pointer transition-colors hover:bg-gray-50"
            :class="{ 'bg-blue-50 border-r-2 border-blue-500': currentChatId === chat.id }"
            @click="selectChat(chat.id)"
          >
            <div
              class="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center text-xl relative"
            >
              {{ chat.avatar }}
              <!-- 在线状态指示器 -->
              <div
                v-if="chat.status === '在线'"
                class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"
              ></div>
            </div>
            <div class="ml-3 flex-1 min-w-0">
              <div class="flex items-center justify-between mb-1">
                <span class="font-medium text-sm text-gray-900">{{ chat.name }}</span>
                <span class="text-xs text-gray-500">{{ formatTime(chat.lastMessageTime) }}</span>
              </div>
              <div class="text-xs text-gray-500 whitespace-nowrap overflow-hidden text-ellipsis">
                {{ chat.lastMessage || '暂无消息' }}
              </div>
              <div class="text-xs text-gray-400 mt-1">
                {{ chat.user.department }} · {{ chat.user.position }}
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div
            v-if="!filteredChats.length && !isLoadingUsers"
            class="flex items-center justify-center p-8"
          >
            <div class="text-gray-500 text-sm">暂无联系人</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col">
      <div class="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 rounded-lg bg-gray-200 flex items-center justify-center">
            {{ currentChat?.avatar || '👤' }}
          </div>
          <div>
            <h3 class="font-medium text-base text-gray-900 m-0">
              {{ currentChat?.name || '选择聊天' }}
            </h3>
            <p class="text-xs text-gray-500 m-0">{{ currentChat?.status || '离线' }}</p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <button
            class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-sm hover:bg-gray-100"
            @click="showMoreOptions"
          >
            ⋯
          </button>
          <button
            class="bg-transparent border-none cursor-pointer p-2 rounded text-red-600 text-sm hover:bg-red-50"
            @click="handleLogout"
          >
            退出登录
          </button>
        </div>
      </div>

      <div class="flex-1 overflow-y-auto p-4" ref="messagesArea">
        <div
          v-if="!currentMessages.length"
          class="flex items-center justify-center h-full text-gray-500 text-sm"
        >
          {{ currentChat ? '暂无消息' : '请选择一个聊天开始对话' }}
        </div>
        <div v-else>
          <div
            v-for="message in currentMessages"
            :key="message.id"
            class="flex items-start gap-3 mb-4"
          >
            <div
              class="w-10 h-10 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0"
            >
              {{ message.senderAvatar }}
            </div>
            <div class="flex-1">
              <div class="flex items-center gap-2 mb-1">
                <span class="font-medium text-sm text-gray-900">{{ message.senderName }}</span>
                <span class="text-xs text-gray-500">ID: {{ message.senderId }}</span>
              </div>
              <div class="text-xs text-gray-500 mb-2">
                {{ formatMessageTime(message.timestamp) }}
              </div>
              <div class="bg-white border border-gray-200 rounded-lg p-3 max-w-lg shadow-sm">
                <div class="text-sm text-gray-900 leading-relaxed" v-html="message.content"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white border-t border-gray-200 min-h-48 p-2" v-if="currentChat">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <button
              class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
              @click="insertEmoji"
            >
              😊
            </button>
            <button
              class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
              @click="attachFile"
            >
              📎
            </button>
            <button
              class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
              @click="insertImage"
            >
              🖼️
            </button>
          </div>
          <button
            class="bg-blue-500 text-white border-none px-4 py-2 rounded-md cursor-pointer text-sm font-medium hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
            @click="sendMessage"
            :disabled="!messageContent.trim()"
          >
            发送 (Ctrl+Enter)
          </button>
        </div>
        <div class="border border-gray-300 rounded-md bg-white">
          <textarea
            ref="messageEditor"
            v-model="messageContent"
            class="w-full border-none outline-none p-3 text-sm font-inherit resize-y min-h-28 placeholder-gray-400"
            placeholder="输入消息..."
            @keydown="handleKeyDown"
            rows="6"
          ></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useUserStore } from '../store/user'
import { apiClient } from '../api'
import type { User } from '../api'
// 用户状态管理
const userStore = useUserStore()

// 响应式数据
const searchQuery = ref('')
const currentChatId = ref<string | null>(null)
const messageContent = ref('')
const messagesArea = ref<HTMLElement>()
const messageEditor = ref<HTMLTextAreaElement>()
const isLoadingUsers = ref(false)

// 聊天数据 - 从API获取的用户列表转换为聊天列表
const chats = ref<
  Array<{
    id: string
    name: string
    avatar: string
    status: string
    lastMessage: string
    lastMessageTime: Date
    user: User
  }>
>([])

// 从API加载用户列表
const loadUsers = async () => {
  try {
    isLoadingUsers.value = true
    const response = await apiClient.getUsers()

    if (response.success) {
      // 将用户列表转换为聊天列表格式
      chats.value = response.users
        .filter((user) => user.id !== userStore.currentUser.value?.id) // 排除当前用户
        .map((user) => ({
          id: user.id,
          name: user.displayName,
          avatar: user.avatar.startsWith('/') ? '👤' : user.avatar, // 如果是路径则使用默认头像
          status: user.isOnline ? '在线' : '离线',
          lastMessage: '暂无消息',
          lastMessageTime: new Date(user.lastOnlineTime),
          user
        }))

      console.log('加载用户列表成功:', chats.value)
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 如果API失败，使用默认数据
    chats.value = [
      {
        id: '1',
        name: '朱钰蒨',
        avatar: '👩',
        status: '在线',
        lastMessage: '你好，最近怎么样？',
        lastMessageTime: new Date(Date.now() - 1000 * 60 * 5),
        user: {
          id: '1',
          username: 'zhuyuqian',
          email: '<EMAIL>',
          displayName: '朱钰蒨',
          avatar: '👩',
          department: '产品部',
          position: '产品经理',
          isOnline: true,
          lastOnlineTime: new Date().toISOString()
        }
      }
    ]
  } finally {
    isLoadingUsers.value = false
  }
}

// 消息数据
const messages = ref<Record<string, any[]>>({
  '1': [
    {
      id: '1',
      senderId: 'user1',
      senderName: '朱钰蒨',
      senderAvatar: '👩',
      content: '你好！很高兴见到你',
      timestamp: new Date(Date.now() - 1000 * 60 * 10)
    },
    {
      id: '2',
      senderId: 'current',
      senderName: userStore.userDisplayName.value || '我',
      senderAvatar: '👤',
      content: '你好！我也很高兴见到你',
      timestamp: new Date(Date.now() - 1000 * 60 * 8)
    }
  ],
  '2': [
    {
      id: '3',
      senderId: 'user2',
      senderName: '张三',
      senderAvatar: '👨',
      content: '明天的会议准备好了吗？',
      timestamp: new Date(Date.now() - 1000 * 60 * 30)
    }
  ],
  '3': []
})

// 计算属性
const filteredChats = computed(() => {
  if (!searchQuery.value.trim()) {
    return chats.value
  }
  return chats.value.filter((chat) =>
    chat.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const currentChat = computed(() => {
  return chats.value.find((chat) => chat.id === currentChatId.value)
})

const currentMessages = computed(() => {
  return currentChatId.value ? messages.value[currentChatId.value] || [] : []
})

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isAuthenticated.value) {
    console.log('用户未登录，应该跳转到登录页')
    return
  }

  // 加载用户列表
  await loadUsers()

  // 默认选择第一个聊天
  if (chats.value.length > 0) {
    currentChatId.value = chats.value[0].id
  }
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}

const handleAddChat = () => {
  console.log('刷新联系人列表')
  loadUsers()
}

const selectChat = (chatId: string) => {
  currentChatId.value = chatId
  nextTick(() => {
    scrollToBottom()
  })
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  // TODO: 实现更多选项功能
}

const handleLogout = () => {
  userStore.logout()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = () => {
  if (!messageContent.value.trim() || !currentChatId.value) {
    return
  }

  const newMessage = {
    id: Date.now().toString(),
    senderId: 'current',
    senderName: userStore.userDisplayName.value || '我',
    senderAvatar: '👤',
    content: messageContent.value.trim(),
    timestamp: new Date()
  }

  // 添加消息到当前聊天
  if (!messages.value[currentChatId.value]) {
    messages.value[currentChatId.value] = []
  }
  messages.value[currentChatId.value].push(newMessage)

  // 更新聊天列表中的最后消息
  const chat = chats.value.find((c) => c.id === currentChatId.value)
  if (chat) {
    chat.lastMessage = messageContent.value.trim()
    chat.lastMessageTime = new Date()
  }

  // 清空输入框
  messageContent.value = ''

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    sendMessage()
  }
}

const scrollToBottom = () => {
  if (messagesArea.value) {
    messagesArea.value.scrollTop = messagesArea.value.scrollHeight
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 1000 * 60) {
    return '刚刚'
  } else if (diff < 1000 * 60 * 60) {
    return `${Math.floor(diff / (1000 * 60))}分钟前`
  } else if (diff < 1000 * 60 * 60 * 24) {
    return `${Math.floor(diff / (1000 * 60 * 60))}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

const formatMessageTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
