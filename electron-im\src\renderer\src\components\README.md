# 聊天组件架构说明

## 组件拆分概述

原来的 `Chat.vue` 组件耦合度过高，包含了太多职责。现在已经拆分为以下独立组件：

## 组件结构

### 1. **UserAvatar.vue** - 用户头像组件
**职责**：显示用户头像
- 支持不同尺寸（small/medium/large）
- 自动提取姓名后两个字作为头像
- 支持在线状态指示器
- 统一的颜色主题 (#4cb7ad)

**Props**：
- `name: string` - 用户姓名
- `size?: 'small' | 'medium' | 'large'` - 头像尺寸
- `isOnline?: boolean` - 是否在线
- `showOnlineStatus?: boolean` - 是否显示在线状态

### 2. **ContactItem.vue** - 联系人项组件
**职责**：显示单个联系人信息
- 显示头像、姓名、最后消息时间
- 显示部门和职位信息
- 处理选中状态
- 时间格式化

**Props**：
- `contact: Contact` - 联系人信息
- `isActive: boolean` - 是否为当前选中

**Events**：
- `select: [id: string]` - 选择联系人

### 3. **ChatSidebar.vue** - 聊天侧边栏组件
**职责**：管理左侧联系人列表
- 搜索功能
- 联系人列表展示
- 加载状态处理
- 空状态处理

**Props**：
- `contacts: Contact[]` - 联系人列表
- `currentContactId: string | null` - 当前选中的联系人ID
- `isLoading: boolean` - 是否正在加载

**Events**：
- `select-contact: [id: string]` - 选择联系人
- `refresh: []` - 刷新联系人列表

### 4. **ChatHeader.vue** - 聊天头部组件
**职责**：显示聊天头部信息
- 当前聊天对象信息
- 操作按钮（更多选项、退出登录）

**Props**：
- `currentContact: Contact | null` - 当前聊天对象

**Events**：
- `more-options: []` - 更多选项
- `logout: []` - 退出登录

### 5. **MessageList.vue** - 消息列表组件
**职责**：显示消息历史
- 消息展示
- 自动滚动到底部
- 空状态处理
- 时间格式化

**Props**：
- `messages: Message[]` - 消息列表
- `currentContact: Contact | null` - 当前聊天对象

**Expose**：
- `scrollToBottom()` - 滚动到底部方法

### 6. **MessageInput.vue** - 消息输入组件
**职责**：处理消息输入和发送
- 消息输入框
- 工具栏按钮
- 快捷键支持（Ctrl+Enter）
- 发送按钮状态管理

**Props**：
- `currentContact: Contact | null` - 当前聊天对象

**Events**：
- `send-message: [content: string]` - 发送消息
- `insert-emoji: []` - 插入表情
- `attach-file: []` - 附加文件
- `insert-image: []` - 插入图片

**Expose**：
- `focus()` - 聚焦输入框
- `clear()` - 清空输入框

## 组件关系图

```
Chat.vue (容器组件)
├── ChatSidebar.vue
│   └── ContactItem.vue
│       └── UserAvatar.vue
├── ChatHeader.vue
│   └── UserAvatar.vue
├── MessageList.vue
│   └── UserAvatar.vue
└── MessageInput.vue
```

## 优势

### 1. **单一职责原则**
每个组件只负责一个特定的功能，职责清晰。

### 2. **可复用性**
- `UserAvatar` 可以在任何需要显示用户头像的地方使用
- `ContactItem` 可以用于其他联系人列表场景
- 各组件都可以独立测试和维护

### 3. **可维护性**
- 代码结构清晰，易于理解和修改
- 组件间通过 props 和 events 通信，耦合度低
- 便于单独测试每个组件

### 4. **可扩展性**
- 新功能可以通过添加新组件或扩展现有组件实现
- 组件可以独立升级而不影响其他部分

## 使用示例

```vue
<template>
  <div class="chat-container">
    <ChatSidebar
      :contacts="contacts"
      :current-contact-id="currentId"
      :is-loading="loading"
      @select-contact="handleSelect"
      @refresh="handleRefresh"
    />
    
    <div class="chat-main">
      <ChatHeader
        :current-contact="currentContact"
        @logout="handleLogout"
      />
      
      <MessageList
        :messages="messages"
        :current-contact="currentContact"
      />
      
      <MessageInput
        :current-contact="currentContact"
        @send-message="handleSendMessage"
      />
    </div>
  </div>
</template>
```

这种架构使得代码更加模块化、可维护和可测试。
