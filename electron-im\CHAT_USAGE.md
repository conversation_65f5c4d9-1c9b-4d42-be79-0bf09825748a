# Electron IM 聊天应用使用说明

## 功能概述

这是一个基于 Electron + Vue 3 + TypeScript 开发的即时通讯应用，模仿了 homework-1 中的聊天界面设计。

## 主要功能

### 1. 用户登录
- 支持用户名密码登录
- 登录状态持久化（使用 localStorage）
- 登录成功后自动跳转到聊天页面

### 2. 聊天界面
- **左侧聊天列表**：
  - 显示所有聊天对话
  - 支持搜索功能
  - 显示最后消息和时间
  - 点击切换聊天

- **右侧聊天区域**：
  - 显示当前聊天的消息历史
  - 支持发送文本消息
  - 显示消息发送者和时间
  - 支持 Ctrl+Enter 快捷发送

### 3. 其他功能
- 退出登录
- 响应式设计
- 现代化 UI 界面

## 测试账号

应用内置了两个测试账号：

1. **管理员账号**
   - 用户名：`admin`
   - 密码：`123456`

2. **朱钰蒨账号**
   - 用户名：`zhu<PERSON>qian`
   - 密码：`123456`

## 运行方式

1. 安装依赖：
   ```bash
   cd electron-im
   npm install
   ```

2. 启动开发服务器：
   ```bash
   npm run start:electron
   ```

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **桌面应用**：Electron
- **样式**：Tailwind CSS + 自定义 CSS
- **状态管理**：Vue 3 Composition API
- **构建工具**：Vite + Electron Vite

## 项目结构

```
electron-im/src/renderer/src/
├── views/
│   ├── Login.vue      # 登录页面
│   └── Chat.vue       # 聊天页面
├── store/
│   ├── user.ts        # 用户状态管理
│   └── message.ts     # 消息状态管理
├── api/
│   └── index.ts       # API 接口（包含模拟登录）
├── config/
│   └── index.ts       # 应用配置
└── App.vue            # 主应用组件（路由逻辑）
```

## 设计特点

1. **模仿 homework-1 设计**：界面布局和样式与原始 HTML 页面保持一致
2. **响应式状态管理**：使用 Vue 3 的响应式系统管理用户状态和聊天数据
3. **组件化架构**：将登录和聊天功能分离为独立组件
4. **类型安全**：使用 TypeScript 确保代码质量

## 后续扩展

- [ ] 集成真实的后端 API
- [ ] 添加 WebSocket 实时通讯
- [ ] 支持文件和图片发送
- [ ] 添加表情包功能
- [ ] 支持群聊功能
- [ ] 添加消息通知
