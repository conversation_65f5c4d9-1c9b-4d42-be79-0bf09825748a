# Electron IM 聊天应用使用说明

## 功能概述

这是一个基于 Electron + Vue 3 + TypeScript 开发的即时通讯应用，模仿了 homework-1 中的聊天界面设计。

## 主要功能

### 1. 用户登录
- 支持用户名密码登录
- 登录状态持久化（使用 localStorage）
- 登录成功后自动跳转到聊天页面

### 2. 聊天界面
- **左侧联系人列表**：
  - 从API动态加载联系人列表
  - 显示用户在线状态（绿色圆点表示在线）
  - 显示用户部门和职位信息
  - 支持搜索功能
  - 支持刷新联系人列表（🔄按钮）
  - 点击切换聊天对象

- **右侧聊天区域**：
  - 显示当前聊天的消息历史
  - 支持发送文本消息
  - 显示消息发送者和时间
  - 支持 Ctrl+Enter 快捷发送

### 3. 其他功能
- 退出登录
- 响应式设计
- 现代化 UI 界面

## 测试账号

应用内置了两个测试账号：

1. **管理员账号**
   - 用户名：`admin`
   - 密码：`123456`

2. **朱钰蒨账号**
   - 用户名：`zhuyuqian`
   - 密码：`123456`

## API 配置

应用支持两种模式：

### 1. 模拟数据模式（默认）
在 `src/renderer/src/config/index.ts` 中设置：
```typescript
export const API_CONFIG = {
  USE_MOCK: true  // 使用模拟数据
}
```

### 2. 真实API模式
在 `src/renderer/src/config/index.ts` 中设置：
```typescript
export const API_CONFIG = {
  USE_MOCK: false,  // 使用真实API
  BASE_URL: 'http://your-api-server.com',  // 设置API服务器地址
}
```

支持的API接口：
- `POST /api/auth/login` - 用户登录
- `GET /api/users` - 获取联系人列表

## 运行方式

1. 安装依赖：
   ```bash
   cd electron-im
   npm install
   ```

2. 启动开发服务器：
   ```bash
   npm run start:electron
   ```

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **桌面应用**：Electron
- **样式**：Tailwind CSS（完全使用 Utility Classes）
- **状态管理**：Vue 3 Composition API
- **构建工具**：Vite + Electron Vite

## 项目结构

```
electron-im/src/renderer/src/
├── views/
│   ├── Login.vue      # 登录页面
│   └── Chat.vue       # 聊天页面
├── store/
│   ├── user.ts        # 用户状态管理
│   └── message.ts     # 消息状态管理
├── api/
│   └── index.ts       # API 接口（包含模拟登录）
├── config/
│   └── index.ts       # 应用配置
└── App.vue            # 主应用组件（路由逻辑）
```

## 设计特点

1. **模仿 homework-1 设计**：界面布局和样式与原始 HTML 页面保持一致
2. **Tailwind CSS 实现**：完全使用 Tailwind 的 Utility Classes，无自定义 CSS
3. **响应式状态管理**：使用 Vue 3 的响应式系统管理用户状态和聊天数据
4. **组件化架构**：将登录和聊天功能分离为独立组件
5. **类型安全**：使用 TypeScript 确保代码质量

## Tailwind CSS 使用示例

聊天页面完全使用 Tailwind CSS 类名实现样式：

```vue
<!-- 左侧聊天列表 -->
<div class="w-80 h-screen bg-white border-r border-gray-200 flex flex-col">
  <!-- 搜索栏 -->
  <div class="p-4 border-b border-gray-200">
    <div class="relative">
      <input class="w-full pl-9 pr-10 py-2 bg-gray-100 border-none rounded-md outline-none text-sm" />
    </div>
  </div>

  <!-- 聊天项目 -->
  <div class="flex items-center p-3 cursor-pointer transition-colors hover:bg-gray-50">
    <div class="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center text-xl">
      👩
    </div>
  </div>
</div>
```

主要使用的 Tailwind 类名：
- **布局**：`flex`, `flex-col`, `w-80`, `h-screen`
- **间距**：`p-4`, `m-3`, `gap-3`
- **颜色**：`bg-white`, `text-gray-900`, `border-gray-200`
- **交互**：`hover:bg-gray-50`, `cursor-pointer`, `transition-colors`

## 后续扩展

- [ ] 集成真实的后端 API
- [ ] 添加 WebSocket 实时通讯
- [ ] 支持文件和图片发送
- [ ] 添加表情包功能
- [ ] 支持群聊功能
- [ ] 添加消息通知
